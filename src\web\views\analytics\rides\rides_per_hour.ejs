<!--Where rides per hour chart should go-->
<canvas id="ridesPerHourChart" width="400" height="200"></canvas>

<script>
  // Función para crear el gráfico de rides per hour
  function createRidesPerHourChart() {
    const ctx = document.getElementById('ridesPerHourChart').getContext('2d');
    
    // Obtener los datos de rides por hora
    const ridesPerHourData = ridesData?.ridesPerHour || [];
    
    // Preparar los datos para Chart.js
    const hours = ridesPerHourData.map(item => `${item.hour}:00`);
    const counts = ridesPerHourData.map(item => item.count);
    
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: hours,
        datasets: [{
          label: 'Rides per Hour',
          data: counts,
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Rides'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Hour of Day'
            }
          }
        }
      }
    });
  }
</script>