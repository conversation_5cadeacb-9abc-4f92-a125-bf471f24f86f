<!--Bar chart: Amount of rides by month-->
<canvas id="ridesPerMonthChart" width="400" height="200"></canvas>

<div class="mt-3">
  <p><strong>Total rides this year: <span id="totalRidesYear">0</span></strong></p>
</div>

<script>
  // Función para crear el gráfico de rides per month
  function createRidesPerMonthChart() {
    const ctx = document.getElementById('ridesPerMonthChart').getContext('2d');

    // Obtener los datos de rides por mes
    const ridesPerMonthData = ridesData?.ridesPerMonth || [];

    // Preparar los datos para Chart.js
    const months = ridesPerMonthData.map(item => item.month);
    const counts = ridesPerMonthData.map(item => item.count);

    // Calcular total de rides del año
    const totalRides = counts.reduce((sum, count) => sum + count, 0);
    document.getElementById('totalRidesYear').textContent = totalRides;

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: months,
        datasets: [{
          label: 'Rides per Month',
          data: counts,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Rides'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Month'
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: 'Monthly Rides Distribution'
          }
        }
      }
    });
  }
</script>