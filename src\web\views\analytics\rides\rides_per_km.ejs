<!--Bar chart: Amount of rides per Km-->
<canvas id="ridesPerKmChart" width="400" height="200"></canvas>

<div class="mt-3">
  <p><strong>80th percentile distance: <span id="percentile80">0</span> km</strong></p>
  <p><strong>Total analyzed rides: <span id="totalAnalyzedRides">0</span></strong></p>
</div>

<script>
  // Función para crear el gráfico de rides per km
  function createRidesPerKmChart() {
    const ctx = document.getElementById('ridesPerKmChart').getContext('2d');

    // Obtener los datos de rides por km
    const ridesPerKmData = ridesData?.ridesPerKm || {};
    const ridesPerKmArray = ridesPerKmData.ridesPerKm || [];
    const percentile80 = ridesPerKmData.percentile80 || 0;
    const totalRides = ridesPerKmData.totalRides || 0;

    // Actualizar información adicional
    document.getElementById('percentile80').textContent = percentile80;
    document.getElementById('totalAnalyzedRides').textContent = totalRides;

    // Preparar los datos para Chart.js
    const distances = ridesPerKmArray.map(item => `${item.distance} km`);
    const counts = ridesPerKmArray.map(item => item.count);

    // Crear colores para las barras, destacando el percentil 80
    const backgroundColors = ridesPerKmArray.map(item => {
      return item.distance <= percentile80 ? 'rgba(75, 192, 192, 0.6)' : 'rgba(255, 99, 132, 0.6)';
    });

    const borderColors = ridesPerKmArray.map(item => {
      return item.distance <= percentile80 ? 'rgba(75, 192, 192, 1)' : 'rgba(255, 99, 132, 1)';
    });

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: distances,
        datasets: [{
          label: 'Rides per Distance',
          data: counts,
          backgroundColor: backgroundColors,
          borderColor: borderColors,
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Rides'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Distance (km)'
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          title: {
            display: true,
            text: 'Rides Distribution by Distance'
          },
          tooltip: {
            callbacks: {
              afterLabel: function(context) {
                const distance = ridesPerKmArray[context.dataIndex].distance;
                if (distance <= percentile80) {
                  return 'Within 80th percentile';
                } else {
                  return 'Above 80th percentile';
                }
              }
            }
          }
        }
      }
    });
  }
</script>